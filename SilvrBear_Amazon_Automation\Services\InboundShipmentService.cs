using SilvrBear_Amazon_Automation.Constants;
using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// High-level service for managing inbound shipments
/// </summary>
public class InboundShipmentService : IInboundShipmentService
{
    private readonly IAmazonSellerApiService _amazonApiService;
    private readonly ILogger<InboundShipmentService> _logger;
    private readonly IConfiguration _configuration;

    public InboundShipmentService(
        IAmazonSellerApiService amazonApiService,
        ILogger<InboundShipmentService> logger,
        IConfiguration configuration)
    {
        _amazonApiService = amazonApiService;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<InboundShipmentResponse> CreateCompleteInboundShipmentAsync(CreateInboundShipmentRequest request)
    {
        _logger.LogInformation("Starting complete inbound shipment creation for {ShipmentName}", request.ShipmentName);
        
        // Initialize inbound plan ID to track it even if later steps fail
        string? inboundPlanId = null;
        
        try
        {

            // Step 1: Validate the request
            var (isValid, errors) = await ValidateShipmentRequestAsync(request);
            if (!isValid)
            {
                return new InboundShipmentResponse
                {
                    Errors = errors,
                    ShipmentName = request.ShipmentName
                };
            }

            // Step 2: Create shipment plan with India ship-from address
            var shipFromAddress = new Address
            {
                Name = _configuration["ShipFromAddress:Name"]!,
                AddressLine1 = _configuration["ShipFromAddress:AddressLine1"]!,
                AddressLine2 = _configuration["ShipFromAddress:AddressLine2"],
                City = _configuration["ShipFromAddress:City"]!,
                StateOrProvinceCode = _configuration["ShipFromAddress:StateOrProvinceCode"]!,
                CountryCode = _configuration["ShipFromAddress:CountryCode"]!,
                PostalCode = _configuration["ShipFromAddress:PostalCode"]!,
                PhoneNumber = _configuration["ShipFromAddress:PhoneNumber"]!
            };

            // Debug: Log the phone number to ensure it's set
            _logger.LogInformation("DEBUG: Ship-from address phone number: '{PhoneNumber}'", shipFromAddress.PhoneNumber);

            // Use the standard API method (Step 1: createInboundPlan without destination FC)
            var planResponse = await _amazonApiService.CreateInboundShipmentPlanAsync(
                request.Items,
                shipFromAddress,
                request.LabelPrepPreference ?? "SELLER_LABEL");

            if (!planResponse.IsSuccess || string.IsNullOrEmpty(planResponse.Payload?.InboundPlanId))
            {
                _logger.LogWarning("Plan creation failed or InboundPlanId is null");
                return new InboundShipmentResponse
                {
                    Errors = planResponse.Errors?.Select(e => e.Message).ToList() ?? new List<string> { "Unknown error" },
                    ShipmentName = request.ShipmentName
                };
            }

            // Step 3: Extract inbound plan ID from the new v2024-03-20 API response
            inboundPlanId = planResponse.Payload.InboundPlanId;

            _logger.LogInformation("Created inbound plan with ID: {InboundPlanId}", inboundPlanId);

            // Step 4: Implement complete India marketplace workflow
            var completeResponse = await CreateCompleteInboundShipmentForIndiaAsync(inboundPlanId, request);

            if (!completeResponse.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    Errors = completeResponse.Errors.Select(e => e.Message).ToList(),
                    ShipmentName = request.ShipmentName,
                    InboundPlanId = inboundPlanId // Always preserve the plan ID even if shipment creation fails
                };
            }

            // Check if this is a partial success (plan created but confirmation failed)
            var isPartialSuccess = completeResponse.Payload != null &&
                                 completeResponse.Payload.ToString()?.Contains("PartialComplete") == true;

            var partialSuccessMessage = string.Empty;
            if (isPartialSuccess)
            {
                _logger.LogInformation("Partial success detected - plan created successfully but confirmation failed due to Amazon API issues");
                partialSuccessMessage = "Plan created successfully but confirmation failed due to Amazon internal errors. You can manually confirm the placement in Amazon Seller Central.";
            }

            // Step 5: Extract shipment ID from the response (use plan ID as fallback)
            var shipmentId = inboundPlanId; // In the new workflow, we use the plan ID

            // Step 6: Get shipment details to return complete information (if available)
            var detailsResponse = await _amazonApiService.GetInboundShipmentAsync(shipmentId);

            var response = new InboundShipmentResponse
            {
                ShipmentId = shipmentId,
                ShipmentName = request.ShipmentName,
                Status = ShipmentStatus.WORKING,
                DestinationFulfillmentCenterId = request.DestinationFulfillmentCenterId,
                Items = request.Items,
                CreatedDate = DateTime.UtcNow,
                LastUpdatedDate = DateTime.UtcNow,
                InboundPlanId = inboundPlanId, // Store the plan ID for future operations
                IsPartialSuccess = isPartialSuccess // Track if this was a partial success due to Amazon API issues
            };

            // Add partial success message as a warning (not an error)
            if (isPartialSuccess && !string.IsNullOrEmpty(partialSuccessMessage))
            {
                response.Errors.Add(partialSuccessMessage);
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating complete inbound shipment");
            return new InboundShipmentResponse
            {
                Errors = new List<string> { $"Unexpected error: {ex.Message}" },
                ShipmentName = request.ShipmentName,
                InboundPlanId = inboundPlanId // Preserve plan ID if it was created before the exception
            };
        }
    }

    public async Task<InboundShipmentResponse> GetShipmentDetailsAsync(string shipmentId)
    {
        try
        {
            var response = await _amazonApiService.GetInboundShipmentAsync(shipmentId);

            if (!response.IsSuccess || response.Payload == null)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = response.Errors.Select(e => e.Message).ToList()
                };
            }

            var shipment = response.Payload;

            return new InboundShipmentResponse
            {
                ShipmentId = shipment.ShipmentId,
                ShipmentName = shipment.ShipmentName,
                Status = Enum.TryParse<ShipmentStatus>(shipment.ShipmentStatus, out var status) ? status : ShipmentStatus.WORKING,
                DestinationFulfillmentCenterId = shipment.DestinationFulfillmentCenterId,
                AreCasesRequired = shipment.AreCasesRequired,
                LabelPrepTypes = new List<string> { shipment.LabelPrepPreference }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipment details for {ShipmentId}", shipmentId);
            return new InboundShipmentResponse
            {
                ShipmentId = shipmentId,
                Errors = new List<string> { $"Error retrieving shipment: {ex.Message}" }
            };
        }
    }

    public async Task<List<InboundShipmentResponse>> GetShipmentsAsync(
        ShipmentStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        try
        {
            var statusList = status.HasValue ? new List<string> { status.Value.ToString() } : null;

            var response = await _amazonApiService.GetInboundShipmentsAsync(
                statusList,
                null,
                fromDate,
                toDate);

            if (!response.IsSuccess || response.Payload == null)
            {
                _logger.LogWarning("Failed to get shipments: {Errors}",
                    string.Join(", ", response.Errors.Select(e => e.Message)));
                return new List<InboundShipmentResponse>();
            }

            return response.Payload.Select(shipment => new InboundShipmentResponse
            {
                ShipmentId = shipment.ShipmentId,
                ShipmentName = shipment.ShipmentName,
                Status = Enum.TryParse<ShipmentStatus>(shipment.ShipmentStatus, out var s) ? s : ShipmentStatus.WORKING,
                DestinationFulfillmentCenterId = shipment.DestinationFulfillmentCenterId,
                AreCasesRequired = shipment.AreCasesRequired
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipments list");
            return new List<InboundShipmentResponse>();
        }
    }

    public async Task<InboundShipmentResponse> UpdateShipmentBoxesAsync(string shipmentId, List<ShipmentBox> boxes)
    {
        try
        {
            // First get current shipment details
            var currentShipment = await GetShipmentDetailsAsync(shipmentId);
            if (currentShipment.Errors.Count != 0)
            {
                return currentShipment;
            }

            // Create update request with box information
            var updateRequest = new CreateInboundShipmentRequest
            {
                ShipmentName = currentShipment.ShipmentName ?? $"Shipment-{shipmentId}",
                DestinationFulfillmentCenterId = currentShipment.DestinationFulfillmentCenterId ?? string.Empty,
                Items = currentShipment.Items,
                Boxes = boxes
            };

            // For the new v2024-03-20 API, we need the inbound plan ID to update packing information
            if (string.IsNullOrEmpty(currentShipment.InboundPlanId))
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = new List<string> { "Cannot update shipment: Inbound plan ID not available" }
                };
            }

            var response = await _amazonApiService.UpdateInboundShipmentAsync(currentShipment.InboundPlanId, updateRequest);

            if (!response.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = response.Errors.Select(e => e.Message).ToList()
                };
            }

            // Return updated shipment details
            return await GetShipmentDetailsAsync(shipmentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating shipment boxes for {ShipmentId}", shipmentId);
            return new InboundShipmentResponse
            {
                ShipmentId = shipmentId,
                Errors = new List<string> { $"Error updating boxes: {ex.Message}" }
            };
        }
    }

    public async Task<InboundShipmentResponse> ConfirmShipmentAsync(string shipmentId)
    {
        try
        {
            // For the new v2024-03-20 API, we need the inbound plan ID to confirm transportation
            // First get shipment details to extract the plan ID
            var shipmentDetails = await GetShipmentDetailsAsync(shipmentId);
            if (shipmentDetails.Errors.Count != 0 || string.IsNullOrEmpty(shipmentDetails.InboundPlanId))
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = new List<string> { "Cannot confirm shipment: Unable to retrieve inbound plan ID" }
                };
            }

            // Generate and confirm transportation options using the new API
            var generateResponse = await _amazonApiService.GenerateTransportationOptionsAsync(shipmentDetails.InboundPlanId);
            if (!generateResponse.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = generateResponse.Errors.Select(e => e.Message).ToList()
                };
            }

            // List transportation options
            var listResponse = await _amazonApiService.ListTransportationOptionsAsync(shipmentDetails.InboundPlanId);
            if (!listResponse.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = listResponse.Errors.Select(e => e.Message).ToList()
                };
            }

            // For now, we'll assume the first transportation option is selected
            // In a real implementation, you might want to let the user choose
            var confirmResponse = await _amazonApiService.ConfirmTransportationOptionsAsync(
                shipmentDetails.InboundPlanId, "default-transport-option");

            if (!confirmResponse.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = confirmResponse.Errors.Select(e => e.Message).ToList()
                };
            }

            // Return updated shipment details
            return await GetShipmentDetailsAsync(shipmentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming shipment {ShipmentId}", shipmentId);
            return new InboundShipmentResponse
            {
                ShipmentId = shipmentId,
                Errors = new List<string> { $"Error confirming shipment: {ex.Message}" }
            };
        }
    }

    public async Task<string> GetShippingLabelsAsync(string shipmentId, string labelType = "UNIQUE")
    {
        try
        {
            var response = await _amazonApiService.GetLabelsAsync(shipmentId, "PackageLabel_Letter_6", labelType);

            if (!response.IsSuccess)
            {
                _logger.LogWarning("Failed to get labels for shipment {ShipmentId}: {Errors}",
                    shipmentId, string.Join(", ", response.Errors.Select(e => e.Message)));
                return string.Empty;
            }

            // In a real implementation, you would extract the PDF data from the response
            // For now, return a placeholder
            return "BASE64_ENCODED_PDF_DATA_PLACEHOLDER";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipping labels for {ShipmentId}", shipmentId);
            return string.Empty;
        }
    }

    public Task<(bool IsValid, List<string> Errors)> ValidateShipmentRequestAsync(CreateInboundShipmentRequest request)
    {
        var errors = new List<string>();

        // Validate basic information
        if (string.IsNullOrWhiteSpace(request.ShipmentName))
            errors.Add("Shipment name is required");

        if (string.IsNullOrWhiteSpace(request.DestinationFulfillmentCenterId))
            errors.Add("Destination fulfillment center ID is required");

        // Validate items
        if (request.Items?.Any() != true)
        {
            errors.Add("At least one item is required");
        }
        else
        {
            for (int i = 0; i < request.Items.Count; i++)
            {
                var item = request.Items[i];
                if (string.IsNullOrWhiteSpace(item.Sku))
                    errors.Add($"Item {i + 1}: SKU is required");
                if (item.Quantity <= 0)
                    errors.Add($"Item {i + 1}: Quantity must be greater than 0");
            }
        }

        // Validate boxes if provided
        if (request.Boxes?.Any() == true)
        {
            for (int i = 0; i < request.Boxes.Count; i++)
            {
                var box = request.Boxes[i];
                if (string.IsNullOrWhiteSpace(box.BoxId))
                    errors.Add($"Box {i + 1}: Box ID is required");

                if (box.Dimensions?.Length <= 0 || box.Dimensions?.Width <= 0 || box.Dimensions?.Height <= 0)
                    errors.Add($"Box {i + 1}: All dimensions must be greater than 0");

                if (box.Weight?.Value <= 0)
                    errors.Add($"Box {i + 1}: Weight must be greater than 0");
            }
        }

        return Task.FromResult((errors.Count == 0, errors));
    }

    public async Task<List<FulfillmentCenter>> GetAvailableFulfillmentCentersAsync()
    {
        // In a real implementation, this would call an Amazon API to get fulfillment centers
        // For now, return common India fulfillment centers
        await Task.Delay(1); // Simulate async operation

        return ShipmentConstants.FulfillmentCenters.FulfillmentCenterDetails
            .Select(fc => new FulfillmentCenter
            {
                FulfillmentCenterId = fc.Key,
                Name = fc.Value.Name,
                Address = fc.Value.Address,
                City = fc.Value.City,
                StateOrProvince = fc.Value.StateOrProvince,
                CountryCode = fc.Value.CountryCode,
                PostalCode = fc.Value.PostalCode
            })
            .ToList();
    }

    public async Task<decimal?> EstimateShippingCostAsync(string shipmentId)
    {
        try
        {
            // For the new v2024-03-20 API, shipping cost estimation is part of transportation options
            // First get shipment details to extract the plan ID
            var shipmentDetails = await GetShipmentDetailsAsync(shipmentId);
            if (shipmentDetails.Errors.Count != 0 || string.IsNullOrEmpty(shipmentDetails.InboundPlanId))
            {
                _logger.LogWarning("Cannot estimate shipping cost for {ShipmentId}: Unable to retrieve inbound plan ID", shipmentId);
                return null;
            }

            // Generate transportation options to get cost estimates
            var generateResponse = await _amazonApiService.GenerateTransportationOptionsAsync(shipmentDetails.InboundPlanId);
            if (!generateResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to generate transportation options for {ShipmentId}: {Errors}",
                    shipmentId, string.Join(", ", generateResponse.Errors.Select(e => e.Message)));
                return null;
            }

            // List transportation options to get cost information
            var listResponse = await _amazonApiService.ListTransportationOptionsAsync(shipmentDetails.InboundPlanId);
            if (!listResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to list transportation options for {ShipmentId}: {Errors}",
                    shipmentId, string.Join(", ", listResponse.Errors.Select(e => e.Message)));
                return null;
            }

            // In a real implementation, you would extract the cost from the transportation options response
            // For now, return a placeholder estimate based on the new API workflow
            return 25.99m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating shipping cost for {ShipmentId}", shipmentId);
            return null;
        }
    }

    public async Task<bool> CancelShipmentAsync(string shipmentId)
    {
        try
        {
            // Get current shipment status
            var shipment = await GetShipmentDetailsAsync(shipmentId);

            if (shipment.Errors.Count != 0)
            {
                _logger.LogWarning("Cannot cancel shipment {ShipmentId}: {Errors}",
                    shipmentId, string.Join(", ", shipment.Errors));
                return false;
            }

            // Check if shipment can be cancelled
            if (shipment.Status == ShipmentStatus.SHIPPED ||
                shipment.Status == ShipmentStatus.RECEIVING ||
                shipment.Status == ShipmentStatus.CANCELLED ||
                shipment.Status == ShipmentStatus.DELETED)
            {
                _logger.LogWarning("Cannot cancel shipment {ShipmentId} in status {Status}",
                    shipmentId, shipment.Status);
                return false;
            }

            // In a real implementation, you would call the Amazon API to cancel the shipment
            // For now, just log the cancellation attempt
            _logger.LogInformation("Shipment {ShipmentId} cancellation requested", shipmentId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling shipment {ShipmentId}", shipmentId);
            return false;
        }
    }

    /// <summary>
    /// Implements the complete India marketplace workflow for shipment creation
    /// Following the 8-step process as documented
    /// </summary>
    private async Task<AmazonApiResponse<object>> CreateCompleteInboundShipmentForIndiaAsync(string inboundPlanId, CreateInboundShipmentRequest request)
    {
        try
        {
            _logger.LogInformation("Starting complete India workflow for inbound plan {InboundPlanId}", inboundPlanId);

            // Step 2: Generate placement options for destination fulfillment centers (India marketplace specific)
            _logger.LogInformation("Step 2: Generating placement options with destination FC {DestinationFC}", request.DestinationFulfillmentCenterId);
            var placementResponse = await _amazonApiService.GeneratePlacementOptionsAsync(
                inboundPlanId, 
                request.DestinationFulfillmentCenterId, 
                request.Items);
            if (!placementResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to generate placement options: {Errors}", 
                    string.Join(", ", placementResponse.Errors.Select(e => e.Message)));
                return placementResponse;
            }

            // Wait for placement options to be generated (async operation)
            await Task.Delay(2000); // Give Amazon time to process

            // List placement options
            _logger.LogInformation("=== INBOUND SHIPMENT SERVICE - LISTING PLACEMENT OPTIONS ===");
            var listPlacementResponse = await _amazonApiService.ListPlacementOptionsAsync(inboundPlanId);
            if (!listPlacementResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to list placement options: {Errors}",
                    string.Join(", ", listPlacementResponse.Errors.Select(e => e.Message)));
                _logger.LogInformation("=== END INBOUND SHIPMENT SERVICE - PLACEMENT OPTIONS (FAILED) ===");
                return new AmazonApiResponse<object>
                {
                    Errors = listPlacementResponse.Errors
                };
            }

            _logger.LogInformation("Successfully retrieved placement options from Amazon API");
            _logger.LogInformation("Placement options count: {Count}", listPlacementResponse.Payload?.PlacementOptions?.Count ?? 0);

            // Extract the first available placement option ID from the API response
            // We must have a valid placement option ID from Amazon to proceed with confirmPlacementOptions
            _logger.LogInformation("=== EXTRACTING PLACEMENT OPTION ID ===");
            var selectedPlacementOptionId = ExtractPlacementOptionId(listPlacementResponse);
            if (string.IsNullOrEmpty(selectedPlacementOptionId))
            {
                _logger.LogError("No valid placement option ID found in Amazon's response. Cannot proceed with shipment confirmation.");
                _logger.LogInformation("=== END INBOUND SHIPMENT SERVICE - PLACEMENT OPTIONS (NO VALID ID) ===");
                return new AmazonApiResponse<object>
                {
                    Errors = new List<ApiError> { new() { Code = "InvalidPlacementOption", Message = "No valid placement option ID returned by Amazon. Cannot confirm placement options without a valid ID from Amazon's API response." } }
                };
            }

            _logger.LogInformation("Selected placement option ID: {PlacementOptionId} (length: {Length})",
                selectedPlacementOptionId, selectedPlacementOptionId.Length);
            _logger.LogInformation("=== END INBOUND SHIPMENT SERVICE - PLACEMENT OPTIONS (SUCCESS) ===");

            // Step 3: Set packing information
            _logger.LogInformation("Step 3: Setting packing information");

            // Add delay to ensure API consistency and avoid rate limiting
            await Task.Delay(3000); // 3 second delay before packing information

            var packingInfo = CreatePackingInformation(request);
            var packingResponse = await _amazonApiService.SetPackingInformationAsync(inboundPlanId, packingInfo);
            if (!packingResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to set packing information: {Errors}", 
                    string.Join(", ", packingResponse.Errors.Select(e => e.Message)));
                return packingResponse;
            }

            // Step 4: Confirm placement options
            _logger.LogInformation("Step 4: Confirming placement options");
            var confirmResponse = await _amazonApiService.ConfirmPlacementOptionForIndiaPlanAsync(inboundPlanId, selectedPlacementOptionId);
            if (!confirmResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to confirm placement options: {Errors}",
                    string.Join(", ", confirmResponse.Errors.Select(e => e.Message)));

                // Check if this is an Amazon internal server error
                var isAmazonInternalError = confirmResponse.Errors.Any(e =>
                    e.Message.Contains("InternalFailure") ||
                    e.Message.Contains("internal error") ||
                    e.Message.Contains("All retry attempts failed") ||
                    e.Code == "InternalFailure");

                if (isAmazonInternalError)
                {
                    _logger.LogWarning("Amazon API internal error detected at Step 4. Plan created successfully through Step 3 - proceeding with partial success.");

                    // Return partial success - the plan was created and configured successfully through Step 3
                    // IsSuccess is computed from Errors.Count == 0, so we create a response with no errors
                    return new AmazonApiResponse<object>
                    {
                        Payload = new { Status = "PartialComplete", Message = "Plan created successfully but confirmation failed due to Amazon internal errors. You can manually confirm the placement in Amazon Seller Central." },
                        Errors = new List<ApiError>() // Empty errors list makes IsSuccess = true
                    };
                }

                return confirmResponse;
            }

            // Step 5: Generate transportation options
            _logger.LogInformation("Step 5: Generating transportation options");
            var transportGenResponse = await _amazonApiService.GenerateTransportationOptionsAsync(inboundPlanId);
            if (!transportGenResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to generate transportation options: {Errors}", 
                    string.Join(", ", transportGenResponse.Errors.Select(e => e.Message)));
                return transportGenResponse;
            }

            // Wait for transportation options to be generated
            await Task.Delay(2000);

            // List transportation options
            var listTransportResponse = await _amazonApiService.ListTransportationOptionsAsync(inboundPlanId);
            if (!listTransportResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to list transportation options: {Errors}", 
                    string.Join(", ", listTransportResponse.Errors.Select(e => e.Message)));
                return listTransportResponse;
            }

            // Step 6: Select and confirm transportation options
            _logger.LogInformation("Step 6: Confirming transportation options");
            var selectedTransportOptionId = "default-transport-option"; // This would come from the API response
            var confirmTransportResponse = await _amazonApiService.ConfirmTransportationOptionsAsync(inboundPlanId, selectedTransportOptionId);
            if (!confirmTransportResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to confirm transportation options: {Errors}", 
                    string.Join(", ", confirmTransportResponse.Errors.Select(e => e.Message)));
                return confirmTransportResponse;
            }

            _logger.LogInformation("Successfully completed India marketplace workflow for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object> { Payload = new { Status = "Complete" } };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in complete India workflow for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Creates packing information from the shipment request
    /// </summary>
    private object CreatePackingInformation(CreateInboundShipmentRequest request)
    {
        // Create packing information based on the request boxes or default boxes
        var boxes = request.Boxes?.Any() == true ? request.Boxes : CreateDefaultBoxes(request.Items);

        return new
        {
            packageGroupings = boxes.Select((box, index) => new
            {
                // PackingGroupId must be exactly 38 characters and only contain [a-zA-Z0-9-]
                // Use deterministic ID based on box index to ensure consistency across retries
                packingGroupId = $"PACK-{index:D3}-{DateTime.UtcNow:yyyyMMddHHmmss}-{index:D14}",
                boxes = new[]
                {
                    new
                    {
                        contentInformationSource = "BOX_CONTENT_PROVIDED",
                        dimensions = new
                        {
                            length = box.Dimensions?.Length ?? 30,
                            width = box.Dimensions?.Width ?? 20,
                            height = box.Dimensions?.Height ?? 15,
                            unitOfMeasurement = "CM"
                        },
                        weight = new
                        {
                            value = box.Weight?.Value ?? 1.0m,
                            unit = "KG"  // Fixed: changed from unitOfMeasurement to unit, and ensure it's not empty
                        },
                        quantity = 1,
                        templateDetails = new
                        {
                            templateName = "STANDARD_BOX"
                        }
                    }
                }
            }).ToList()
        };
    }

    /// <summary>
    /// Creates default boxes when none are provided
    /// </summary>
    private List<ShipmentBox> CreateDefaultBoxes(List<ShipmentItem> items)
    {
        // Create one default box for all items
        var totalWeight = (decimal)(items.Sum(item => (item.Quantity * 0.25)) + 0.1); // 250g per item + 100g box weight

        return new List<ShipmentBox>
        {
            new ShipmentBox
            {
                BoxId = "BOX001",
                Dimensions = new BoxDimensions
                {
                    Length = 40,
                    Width = 30,
                    Height = 20,
                    Unit = "CM"
                },
                Weight = new BoxWeight
                {
                    Value = totalWeight,
                    Unit = "KG"
                }
            }
        };
    }

    /// <summary>
    /// Extracts a valid placement option ID from the placement options API response.
    /// Returns null if no valid placement option ID is found - no fallback IDs are generated.
    /// </summary>
    private string? ExtractPlacementOptionId(AmazonApiResponse<PlacementOptionsResponse> placementResponse)
    {
        try
        {
            _logger.LogInformation("=== EXTRACT PLACEMENT OPTION ID DEBUG ===");
            _logger.LogInformation("Response IsSuccess: {IsSuccess}", placementResponse.IsSuccess);
            _logger.LogInformation("Response Payload is null: {IsNull}", placementResponse.Payload == null);

            if (placementResponse.Payload != null)
            {
                _logger.LogInformation("PlacementOptions is null: {IsNull}", placementResponse.Payload.PlacementOptions == null);
                _logger.LogInformation("PlacementOptions count: {Count}", placementResponse.Payload.PlacementOptions?.Count ?? 0);

                // Log complete response for debugging
                _logger.LogInformation("Complete placement response: {Response}",
                    System.Text.Json.JsonSerializer.Serialize(placementResponse.Payload, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));
            }

            // Extract placement option ID from the strongly-typed response
            if (placementResponse.Payload?.PlacementOptions?.Count > 0)
            {
                var firstOption = placementResponse.Payload.PlacementOptions[0];
                var placementOptionId = firstOption.PlacementOptionId;

                _logger.LogInformation("First placement option details:");
                _logger.LogInformation("  PlacementOptionId: '{PlacementOptionId}'", placementOptionId);
                _logger.LogInformation("  Status: '{Status}'", firstOption.Status);
                _logger.LogInformation("  ShipmentIds count: {Count}", firstOption.ShipmentIds?.Count ?? 0);

                if (!string.IsNullOrEmpty(placementOptionId))
                {
                    _logger.LogInformation("Successfully extracted placement option ID: {PlacementOptionId} (length: {Length})",
                        placementOptionId, placementOptionId.Length);
                    _logger.LogInformation("=== END EXTRACT PLACEMENT OPTION ID DEBUG (SUCCESS) ===");
                    return placementOptionId;
                }
                else
                {
                    _logger.LogWarning("Placement option ID is null or empty");
                }
            }
            else
            {
                _logger.LogWarning("No placement options found in response payload");
            }

            // No fallback - if Amazon doesn't provide a valid placement option ID, we cannot proceed
            _logger.LogError("No valid placement option ID found in Amazon's response. Cannot proceed with confirmPlacementOptions API call.");
            _logger.LogInformation("=== END EXTRACT PLACEMENT OPTION ID DEBUG (NO VALID ID) ===");

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting placement option ID from response");
            _logger.LogInformation("=== END EXTRACT PLACEMENT OPTION ID DEBUG (ERROR) ===");
            return null;
        }
    }
}